<?php
declare(strict_types=1);

namespace App\Application\Session;

use App\Models\Auth\Login;

/**
 * Session Manager
 *
 * Clase profesional para el manejo de sesiones de usuario
 * Usa el modelo Login como núcleo para todos los datos de usuario
 * Se enfoca únicamente en el manejo de la sesión PHP
 */
class SessionManager
{
    /**
     * Session keys constants
     */
    private const SESSION_KEY_LOGIN_MODEL = 'login_model_data';
    private const SESSION_KEY_IS_AUTHENTICATED = 'is_authenticated';
    private const SESSION_KEY_LAST_ACTIVITY = 'last_activity';

    /**
     * @var bool Session started flag
     */
    private bool $sessionStarted = false;

    /**
     * @var int Session timeout in seconds (default: 2 hours)
     */
    private int $sessionTimeout = 7200;

    /**
     * @var Login Login model instance (core of session data)
     */
    private Login $loginModel;

    /**
     * Constructor
     *
     * @param Login|null $loginModel Optional login model instance
     */
    public function __construct(?Login $loginModel = null)
    {
        // Initialize login model
        $this->loginModel = $loginModel ?? new Login();

        // Configure session settings
        $this->configureSession();
    }

    /**
     * Configure session settings
     * 
     * @return void
     */
    private function configureSession(): void
    {
        // Set session configuration
        ini_set('session.cookie_httponly', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.cookie_secure', '0'); // Set to 1 in production with HTTPS
        ini_set('session.cookie_samesite', 'Strict');
        
        // Set session name
        session_name('INTEGRATION_SESSION');
    }

    /**
     * Start session
     *
     * @return bool
     */
    public function start(): bool
    {
        if ($this->sessionStarted) {
            return true;
        }

        if (session_status() === PHP_SESSION_NONE) {
            $result = session_start();
            $this->sessionStarted = $result;

            if ($result) {
                $this->loadLoginModelFromSession();
                $this->updateLastActivity();
                $this->validateSession();
            }

            return $result;
        }

        $this->sessionStarted = true;
        $this->loadLoginModelFromSession();
        return true;
    }

    /**
     * Load login model from session data
     *
     * @return void
     */
    private function loadLoginModelFromSession(): void
    {
        $modelData = $_SESSION[self::SESSION_KEY_LOGIN_MODEL] ?? [];
        if (!empty($modelData)) {
            $this->loginModel = new Login($modelData);
        }
    }

    /**
     * Save login model to session
     *
     * @return void
     */
    private function saveLoginModelToSession(): void
    {
        $_SESSION[self::SESSION_KEY_LOGIN_MODEL] = $this->loginModel->toArray();
    }

    /**
     * Validate session timeout
     * 
     * @return void
     */
    private function validateSession(): void
    {
        $lastActivity = $this->getLastActivity();
        
        if ($lastActivity && (time() - $lastActivity) > $this->sessionTimeout) {
            $this->destroy();
        }
    }

    /**
     * Update last activity timestamp
     *
     * @return void
     */
    private function updateLastActivity(): void
    {
        $_SESSION[self::SESSION_KEY_LAST_ACTIVITY] = time();
    }

    /**
     * Get login model instance
     *
     * @return Login
     */
    public function getLoginModel(): Login
    {
        if ($this->loginModel === null) {
            $this->start();

            // Try to load from session
            $modelData = $_SESSION[self::SESSION_KEY_LOGIN_MODEL] ?? [];
            $this->loginModel = new Login($modelData);
        }

        return $this->loginModel;
    }

    /**
     * Save login model to session
     *
     * @return void
     */
    private function saveLoginModel(): void
    {
        if ($this->loginModel !== null) {
            $_SESSION[self::SESSION_KEY_LOGIN_MODEL] = $this->loginModel->toArray();
        }
    }

    /**
     * Create login attempt
     *
     * @param array $loginData Login attempt data
     * @return Login
     */
    public function createLoginAttempt(array $loginData): Login
    {
        $this->start();

        // Update login model with attempt data
        $this->loginModel->setUsername($loginData['username'] ?? '');
        $this->loginModel->setPassword($loginData['password'] ?? '');
        $this->loginModel->setEmail($loginData['email'] ?? '');
        $this->loginModel->setIpAddress($loginData['ip_address'] ?? '');
        $this->loginModel->setUserAgent($loginData['user_agent'] ?? '');
        $this->loginModel->setRememberMe($loginData['remember_me'] ?? false);

        return $this->loginModel;
    }

    /**
     * Validate login attempt
     *
     * @return array Validation errors (empty if valid)
     */
    public function validateLoginAttempt(): array
    {
        return $this->loginModel->validate();
    }

    /**
     * Check if user is authenticated
     *
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        $this->start();
        return isset($_SESSION[self::SESSION_KEY_IS_AUTHENTICATED]) &&
               $_SESSION[self::SESSION_KEY_IS_AUTHENTICATED] === true &&
               $this->loginModel->getIsSuccessful();
    }

    /**
     * Create login attempt
     *
     * @param array $loginData Login attempt data
     * @return Login
     */
    public function createLoginAttempt(array $loginData): Login
    {
        $this->start();

        // Update login model with attempt data
        $this->loginModel->setUsername($loginData['username'] ?? '');
        $this->loginModel->setPassword($loginData['password'] ?? '');
        $this->loginModel->setEmail($loginData['email'] ?? '');
        $this->loginModel->setIpAddress($loginData['ip_address'] ?? '');
        $this->loginModel->setUserAgent($loginData['user_agent'] ?? '');
        $this->loginModel->setRememberMe($loginData['remember_me'] ?? false);

        return $this->loginModel;
    }

    /**
     * Complete successful login
     *
     * @param array $userData User data from authentication
     * @return bool
     */
    public function login(array $userData): bool
    {
        $this->start();

        // Regenerate session ID for security
        session_regenerate_id(true);

        // Update login model with user data
        $this->loginModel->setIsSuccessful(true);
        $this->loginModel->setLoginTime(date('Y-m-d H:i:s'));

        // Set additional user data in login model
        if (isset($userData['user_id'])) {
            $this->loginModel->setUserId($userData['user_id']);
        }
        if (isset($userData['first_name'])) {
            $this->loginModel->setFirstName($userData['first_name']);
        }
        if (isset($userData['last_name'])) {
            $this->loginModel->setLastName($userData['last_name']);
        }

        // Mark as authenticated in session
        $_SESSION[self::SESSION_KEY_IS_AUTHENTICATED] = true;

        // Save login model to session
        $this->saveLoginModelToSession();

        $this->updateLastActivity();

        return true;
    }

    /**
     * Record failed login attempt
     *
     * @param string $reason Failure reason
     * @return void
     */
    public function recordFailedLogin(string $reason): void
    {
        $this->start();
        $this->loginModel->setIsSuccessful(false);
        $this->loginModel->setFailureReason($reason);
        $this->loginModel->incrementAttemptsCount();

        $this->saveLoginModelToSession();
    }

    /**
     * Logout user
     *
     * @return bool
     */
    public function logout(): bool
    {
        $this->start();

        // Reset login model
        $this->loginModel = new Login();

        // Clear all session data
        $_SESSION = [];

        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // Destroy session
        return session_destroy();
    }

    /**
     * Destroy session
     *
     * @return bool
     */
    public function destroy(): bool
    {
        return $this->logout();
    }

    /**
     * Get login model instance
     *
     * @return Login
     */
    public function getLoginModel(): Login
    {
        $this->start();
        return $this->loginModel;
    }

    // ========================================
    // DELEGATION METHODS TO LOGIN MODEL
    // ========================================

    /**
     * Get user ID
     *
     * @return int|null
     */
    public function getUserId(): ?int
    {
        return $this->loginModel->getUserId();
    }

    /**
     * Get username
     *
     * @return string|null
     */
    public function getUsername(): ?string
    {
        return $this->loginModel->getUsername();
    }

    /**
     * Get email
     *
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->loginModel->getEmail();
    }

    /**
     * Get first name
     *
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        return $this->loginModel->getFirstName();
    }

    /**
     * Get last name
     *
     * @return string|null
     */
    public function getLastName(): ?string
    {
        return $this->loginModel->getLastName();
    }

    /**
     * Get full name
     *
     * @return string
     */
    public function getFullName(): string
    {
        return $this->loginModel->getFullName();
    }

    /**
     * Get login attempts count
     *
     * @return int
     */
    public function getLoginAttemptsCount(): int
    {
        return $this->loginModel->getAttemptsCount();
    }

    /**
     * Get last login failure reason
     *
     * @return string|null
     */
    public function getLastFailureReason(): ?string
    {
        return $this->loginModel->getFailureReason();
    }

    /**
     * Check if current login was successful
     *
     * @return bool
     */
    public function isLoginSuccessful(): bool
    {
        return $this->loginModel->getIsSuccessful();
    }

    /**
     * Get IP address from login model
     *
     * @return string|null
     */
    public function getLoginIpAddress(): ?string
    {
        return $this->loginModel->getIpAddress();
    }

    /**
     * Get user agent from login model
     *
     * @return string|null
     */
    public function getLoginUserAgent(): ?string
    {
        return $this->loginModel->getUserAgent();
    }

    /**
     * Check if remember me was selected
     *
     * @return bool
     */
    public function isRememberMe(): bool
    {
        return $this->loginModel->getRememberMe();
    }

    /**
     * Get login time
     *
     * @return string|null
     */
    public function getLoginTime(): ?string
    {
        return $this->loginModel->getLoginTime();
    }

    // ========================================
    // SESSION-SPECIFIC METHODS
    // ========================================

    /**
     * Get last activity time
     *
     * @return int|null
     */
    public function getLastActivity(): ?int
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_LAST_ACTIVITY] ?? null;
    }

    /**
     * Set session timeout
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function setSessionTimeout(int $timeout): void
    {
        $this->sessionTimeout = $timeout;
    }

    /**
     * Get session timeout
     *
     * @return int
     */
    public function getSessionTimeout(): int
    {
        return $this->sessionTimeout;
    }

    /**
     * Check if session is expired
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        $lastActivity = $this->getLastActivity();

        if (!$lastActivity) {
            return true;
        }

        return (time() - $lastActivity) > $this->sessionTimeout;
    }

    /**
     * Get session data as array
     *
     * @return array
     */
    public function toArray(): array
    {
        $this->start();

        return [
            'user_id' => $this->getUserId(),
            'username' => $this->getUsername(),
            'email' => $this->getEmail(),
            'first_name' => $this->getFirstName(),
            'last_name' => $this->getLastName(),
            'full_name' => $this->getFullName(),
            'is_authenticated' => $this->isAuthenticated(),
            'login_time' => $this->getLoginTime(),
            'last_activity' => $this->getLastActivity(),
            'is_expired' => $this->isExpired(),
            'login_attempts' => $this->getLoginAttemptsCount(),
            'last_failure_reason' => $this->getLastFailureReason(),
            'login_ip' => $this->getLoginIpAddress(),
            'remember_me' => $this->isRememberMe()
        ];
    }

    /**
     * Get session data for logging (without sensitive information)
     *
     * @return array
     */
    public function toLogArray(): array
    {
        $sessionData = $this->toArray();

        // Merge with login model log data (which excludes password)
        return array_merge($sessionData, [
            'login_model' => $this->loginModel->toLogArray()
        ]);
    }
}
