<?php
declare(strict_types=1);

namespace App\Middleware;

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface as RequestHandler;
use Psr\Log\LoggerInterface;
use Slim\Psr7\Response as SlimResponse;
use App\Application\Session\SessionManager;

/**
 * Session Middleware
 * 
 * Middleware para controlar sesiones de usuario en rutas protegidas
 * Redirige a login si el usuario no está autenticado
 */
class SessionMiddleware implements MiddlewareInterface
{
    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var SessionManager
     */
    private SessionManager $sessionManager;

    /**
     * Constructor
     * 
     * @param SessionManager|null $sessionManager Optional session manager instance
     * @param LoggerInterface|null $logger Optional logger instance
     */
    public function __construct(?SessionManager $sessionManager = null, ?LoggerInterface $logger = null)
    {
        $this->sessionManager = $sessionManager ?? new SessionManager();
        $this->logger = $logger ?? new \Psr\Log\NullLogger();
    }

    /**
     * Process the request
     * 
     * @param Request $request The request
     * @param RequestHandler $handler The request handler
     * @return Response The response
     */
    public function process(Request $request, RequestHandler $handler): Response
    {
        // Iniciar sesión si no está iniciada
        $this->sessionManager->start();
        
        // Verificar si el usuario está autenticado
        if (!$this->sessionManager->isAuthenticated()) {
            $this->logger->info('Session middleware: User not authenticated, redirecting to login');
            
            // Crear respuesta de redirección a login
            $response = new SlimResponse();
            return $response
                ->withHeader('Location', '/')
                ->withStatus(302);
        }
        
        // Usuario autenticado, continuar con la solicitud
        $this->logger->info('Session middleware: User authenticated', [
            'user_id' => $this->sessionManager->getUserId(),
            'username' => $this->sessionManager->getUsername()
        ]);
        
        // Agregar información de sesión al request para uso posterior
        $request = $request->withAttribute('session', $this->sessionManager);
        $request = $request->withAttribute('user_id', $this->sessionManager->getUserId());
        $request = $request->withAttribute('username', $this->sessionManager->getUsername());
        
        return $handler->handle($request);
    }
}
