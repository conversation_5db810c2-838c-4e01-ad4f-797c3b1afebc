<?php
declare(strict_types=1);

namespace App\Models\Auth;

use App\Models\AbstractModel;

class Login extends AbstractModel
{
    /**
     * Model field constants
     */
    public const FIELD_USERNAME = 'username';
    public const FIELD_PASSWORD = 'password';
    public const FIELD_EMAIL = 'email';
    public const FIELD_REMEMBER_ME = 'remember_me';
    public const FIELD_LOGIN_TIME = 'login_time';
    public const FIELD_IP_ADDRESS = 'ip_address';
    public const FIELD_USER_AGENT = 'user_agent';
    public const FIELD_IS_SUCCESSFUL = 'is_successful';
    public const FIELD_FAILURE_REASON = 'failure_reason';
    public const FIELD_ATTEMPTS_COUNT = 'attempts_count';

    // Additional user data fields
    public const FIELD_USER_ID = 'user_id';
    public const FIELD_FIRST_NAME = 'first_name';
    public const FIELD_LAST_NAME = 'last_name';

    /**
     * Constructor
     * 
     * @param array $data Initial data
     */
    public function __construct(array $data = [])
    {
        parent::__construct($data);
        
        // Set default values
        $this->setData(self::FIELD_LOGIN_TIME, date('Y-m-d H:i:s'));
        $this->setData(self::FIELD_REMEMBER_ME, false);
        $this->setData(self::FIELD_IS_SUCCESSFUL, false);
        $this->setData(self::FIELD_ATTEMPTS_COUNT, 0);
    }

    /**
     * Get username
     * 
     * @return string|null
     */
    public function getUsername(): ?string
    {
        return $this->getData(self::FIELD_USERNAME);
    }

    /**
     * Set username
     * 
     * @param string $username
     * @return $this
     */
    public function setUsername(string $username): self
    {
        return $this->setData(self::FIELD_USERNAME, trim($username));
    }

    /**
     * Get password
     * 
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->getData(self::FIELD_PASSWORD);
    }

    /**
     * Set password
     * 
     * @param string $password
     * @return $this
     */
    public function setPassword(string $password): self
    {
        $password = $this->paswordHash($password);
        return $this->setData(self::FIELD_PASSWORD, $password);
    }

    /**
     * Get email
     * 
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->getData(self::FIELD_EMAIL);
    }

    /**
     * Set email
     * 
     * @param string $email
     * @return $this
     */
    public function setEmail(string $email): self
    {
        return $this->setData(self::FIELD_EMAIL, strtolower(trim($email)));
    }

    /**
     * Get remember me flag
     * 
     * @return bool
     */
    public function getRememberMe(): bool
    {
        return (bool) $this->getData(self::FIELD_REMEMBER_ME, false);
    }

    /**
     * Set remember me flag
     * 
     * @param bool $rememberMe
     * @return $this
     */
    public function setRememberMe(bool $rememberMe): self
    {
        return $this->setData(self::FIELD_REMEMBER_ME, $rememberMe);
    }

    /**
     * Get login time
     * 
     * @return string|null
     */
    public function getLoginTime(): ?string
    {
        return $this->getData(self::FIELD_LOGIN_TIME);
    }

    /**
     * Set login time
     * 
     * @param string $loginTime
     * @return $this
     */
    public function setLoginTime(string $loginTime): self
    {
        return $this->setData(self::FIELD_LOGIN_TIME, $loginTime);
    }

    /**
     * Get IP address
     * 
     * @return string|null
     */
    public function getIpAddress(): ?string
    {
        return $this->getData(self::FIELD_IP_ADDRESS);
    }

    /**
     * Set IP address
     * 
     * @param string $ipAddress
     * @return $this
     */
    public function setIpAddress(string $ipAddress): self
    {
        return $this->setData(self::FIELD_IP_ADDRESS, $ipAddress);
    }

    /**
     * Get user agent
     * 
     * @return string|null
     */
    public function getUserAgent(): ?string
    {
        return $this->getData(self::FIELD_USER_AGENT);
    }

    /**
     * Set user agent
     * 
     * @param string $userAgent
     * @return $this
     */
    public function setUserAgent(string $userAgent): self
    {
        return $this->setData(self::FIELD_USER_AGENT, $userAgent);
    }

    /**
     * Get is successful flag
     * 
     * @return bool
     */
    public function getIsSuccessful(): bool
    {
        return (bool) $this->getData(self::FIELD_IS_SUCCESSFUL, false);
    }

    /**
     * Set is successful flag
     * 
     * @param bool $isSuccessful
     * @return $this
     */
    public function setIsSuccessful(bool $isSuccessful): self
    {
        return $this->setData(self::FIELD_IS_SUCCESSFUL, $isSuccessful);
    }

    /**
     * Get failure reason
     * 
     * @return string|null
     */
    public function getFailureReason(): ?string
    {
        return $this->getData(self::FIELD_FAILURE_REASON);
    }

    /**
     * Set failure reason
     * 
     * @param string $failureReason
     * @return $this
     */
    public function setFailureReason(string $failureReason): self
    {
        return $this->setData(self::FIELD_FAILURE_REASON, $failureReason);
    }

    /**
     * Get attempts count
     * 
     * @return int
     */
    public function getAttemptsCount(): int
    {
        return (int) $this->getData(self::FIELD_ATTEMPTS_COUNT, 0);
    }

    /**
     * Set attempts count
     * 
     * @param int $attemptsCount
     * @return $this
     */
    public function setAttemptsCount(int $attemptsCount): self
    {
        return $this->setData(self::FIELD_ATTEMPTS_COUNT, $attemptsCount);
    }

    /**
     * Increment attempts count
     * 
     * @return $this
     */
    public function incrementAttemptsCount(): self
    {
        $currentCount = $this->getAttemptsCount();
        return $this->setAttemptsCount($currentCount + 1);
    }

    /**
     * Validate login data
     * 
     * @return array Array of validation errors (empty if valid)
     */
    public function validate(): array
    {
        $errors = [];

        // Validate username/email
        $username = $this->getUsername();
        if (empty($username)) {
            $errors[] = 'Username or email is required';
        } elseif (strlen($username) < 3) {
            $errors[] = 'Username must be at least 3 characters long';
        }

        // Validate password
        $password = $this->getPassword();
        if (empty($password)) {
            $errors[] = 'Password is required';
        } elseif (strlen($password) < 6) {
            $errors[] = 'Password must be at least 6 characters long';
        }

        // Validate email format if provided
        $email = $this->getEmail();
        if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        }

        return $errors;
    }

    /**
     * Check if login data is valid
     * 
     * @return bool
     */
    public function isValid(): bool
    {
        return empty($this->validate());
    }

    /**
     * Get login identifier (username or email)
     * 
     * @return string|null
     */
    public function getLoginIdentifier(): ?string
    {
        $username = $this->getUsername();
        
        // If username looks like an email, return it as email
        if ($username && filter_var($username, FILTER_VALIDATE_EMAIL)) {
            $this->setEmail($username);
            return $username;
        }
        
        return $username;
    }

    /**
     * Reset login data
     * 
     * @return $this
     */
    public function reset(): self
    {
        $this->setData([
            self::FIELD_USERNAME => null,
            self::FIELD_PASSWORD => null,
            self::FIELD_EMAIL => null,
            self::FIELD_REMEMBER_ME => false,
            self::FIELD_IS_SUCCESSFUL => false,
            self::FIELD_FAILURE_REASON => null,
            self::FIELD_ATTEMPTS_COUNT => 0,
            self::FIELD_LOGIN_TIME => date('Y-m-d H:i:s')
        ]);

        return $this;
    }

    /**
     * Get user ID
     *
     * @return int|null
     */
    public function getUserId(): ?int
    {
        return $this->getData(self::FIELD_USER_ID);
    }

    /**
     * Set user ID
     *
     * @param int $userId
     * @return $this
     */
    public function setUserId(int $userId): self
    {
        return $this->setData(self::FIELD_USER_ID, $userId);
    }

    /**
     * Get first name
     *
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        return $this->getData(self::FIELD_FIRST_NAME);
    }

    /**
     * Set first name
     *
     * @param string $firstName
     * @return $this
     */
    public function setFirstName(string $firstName): self
    {
        return $this->setData(self::FIELD_FIRST_NAME, trim($firstName));
    }

    /**
     * Get last name
     *
     * @return string|null
     */
    public function getLastName(): ?string
    {
        return $this->getData(self::FIELD_LAST_NAME);
    }

    /**
     * Set last name
     *
     * @param string $lastName
     * @return $this
     */
    public function setLastName(string $lastName): self
    {
        return $this->setData(self::FIELD_LAST_NAME, trim($lastName));
    }

    /**
     * Get full name
     *
     * @return string
     */
    public function getFullName(): string
    {
        $firstName = $this->getFirstName();
        $lastName = $this->getLastName();

        if ($firstName && $lastName) {
            return trim($firstName . ' ' . $lastName);
        }

        return $firstName ?: $lastName ?: $this->getUsername() ?: 'Usuario';
    }

    /**
     * Convert to array for logging (without sensitive data)
     *
     * @return array
     */
    public function toLogArray(): array
    {
        $data = $this->toArray();

        // Remove sensitive data
        unset($data[self::FIELD_PASSWORD]);

        return $data;
    }

    public function paswordHash($password)
    {
        $options = [
            'cost' => 15,
        ];
        return password_hash($password, PASSWORD_BCRYPT, $options );
    }
}
