<?php
declare(strict_types=1);

namespace App\Controllers\Pages;

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\LoggerInterface;
use Slim\Views\Twig;
use App\Application\Session\SessionManager;

class IndexController
{
    protected LoginPage $loginPage;
    protected DashboarPage $dashboarPage;
    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var SessionManager
     */
    private SessionManager $sessionManager;

    /**
     * @param LoggerInterface $logger
     * @param LoginPage $loginPage
     * @param SessionManager|null $sessionManager
     */
    public function __construct(
        LoggerInterface $logger,
        LoginPage $loginPage,
        DashboarPage $dashboarPage,
        ?SessionManager $sessionManager = null
    ) {
        $this->logger = $logger;
        $this->sessionManager = $sessionManager ?? new SessionManager();
        $this->loginPage = $loginPage;
        $this->dashboarPage = $dashboarPage;
    }

    /**
     *
     *
     * @param Request $request
     * @param Response $response
     * @param array $args
     * @return Response
     */
    public function index(Request $request, Response $response, array $args): Response
    {
        $this->sessionManager->start();
        print_r($_SESSION);die();
        if ($this->sessionManager->isAuthenticated()) {
            $this->logger->info('User authenticated, redirecting to dashboard', [
                'user_id' => $this->sessionManager->getUserId(),
                'username' => $this->sessionManager->getUsername()
            ]);
            return $this->dashboarPage->IndexPage($request, $response, $args);
        } else {
            $this->logger->info('User not authenticated, redirecting to login', [
                'user_id' => $this->sessionManager->getUserId(),
                'username' => $this->sessionManager->getUsername()
            ]);
            return $this->loginPage->IndexPage($request, $response, $args);
        }
    }
}
