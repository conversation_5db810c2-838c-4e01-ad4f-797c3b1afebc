<?php
declare(strict_types=1);

namespace App\Models\Pages\Users;

use App\Models\AbstractModel;
use App\Models\Pages\Users\ApiUser;


/**
 * Class StockRepository
 *
 * Repository for stock data implementing ApiStock interface
 * and extending AbstractModel for Magento-like functionality
 */
class UserRepository extends AbstractModel implements ApiUser
{
    /**
     * @inheritDoc
     */
    public function getId(): int
    {
        return (int)$this->getData(self::API_USER_ID, 0);
    }

    /**
     * @inheritDoc
     */
    public function getUsername(): string
    {
        return (string)$this->getData(self::API_USER_USERNAME, '');
    }

    /**
     * @inheritDoc
     */
    public function getEmail(): string
    {
        return (string)$this->getData(self::API_USER_EMAIL, '');
    }

    /**
     * @inheritDoc
     */
    public function getPasswordHash(): string
    {
        return (string)$this->getData(self::API_USER_PASSWORD_HASH, '');
    }

    /**
     * @inheritDoc
     */
    public function getFirstName(): string
    {
        return (string)$this->getData(self::API_USER_FIRST_NAME, '');
    }

    /**
     * @inheritDoc
     */
    public function getLastName(): string
    {
        return (string)$this->getData(self::API_USER_LAST_NAME, '');
    }

    /**
     * @inheritDoc
     */
    public function getIsActive(): bool
    {
        return (bool)$this->getData(self::API_USER_IS_ACTIVE, false);
    }

    /**
     * @inheritDoc
     */
    public function getCreatedAt(): string
    {
        return (string)$this->getData(self::API_USER_CREATED_AT, '');
    }

    /**
     * @inheritDoc
     */
    public function getUpdatedAt(): string
    {
        return (string)$this->getData(self::API_USER_UPDATED_AT, '');
    }

    public function setId(int $id): void
    {
        $this->setData(self::API_USER_ID, $id);
    }

    public function setUsername(string $username): void
    {
        $this->setData(self::API_USER_USERNAME, $username);
    }

    public function setEmail(string $email): void
    {
        $this->setData(self::API_USER_EMAIL, $email);
    }

    public function setPasswordHash(string $passwordHash): void
    {
        $this->setData(self::API_USER_PASSWORD_HASH, $passwordHash);
    }

    public function setFirstName(string $firstName): void
    {
        $this->setData(self::API_USER_FIRST_NAME, $firstName);
    }

    public function setLastName(string $lastName): void
    {
        $this->setData(self::API_USER_LAST_NAME, $lastName);
    }

    public function setIsActive(bool $isActive): void
    {
        $this->setData(self::API_USER_IS_ACTIVE, $isActive);
    }

    public function setCreatedAt(string $createdAt): void
    {
        $this->setData(self::API_USER_CREATED_AT, $createdAt);
    }

    public function setUpdatedAt(string $updatedAt): void
    {
        $this->setData(self::API_USER_UPDATED_AT, $updatedAt);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::API_USER_ID => $this->getId(),
            self::API_USER_USERNAME => $this->getUsername(),
            self::API_USER_EMAIL => $this->getEmail(),
            self::API_USER_PASSWORD_HASH => $this->getPasswordHash(),
            self::API_USER_FIRST_NAME => $this->getFirstName(),
            self::API_USER_LAST_NAME => $this->getLastName(),
            self::API_USER_IS_ACTIVE => $this->getIsActive(),
            self::API_USER_CREATED_AT => $this->getCreatedAt(),
            self::API_USER_UPDATED_AT => $this->getUpdatedAt()
        ];
    }

    public static function fromArray(array $data): self
    {
        $userData = new self();
        $userData->setId((int)$data['user_id']);
        $userData->setUsername($data['username']);
        $userData->setEmail($data['email']);
        $userData->setPasswordHash($data['password_hash']);
        $userData->setFirstName($data['first_name']);
        $userData->setLastName($data['last_name']);
        $userData->setIsActive((bool)$data['is_active']);
        $userData->setCreatedAt($data['created_at']);
        $userData->setUpdatedAt($data['updated_at']);

        return $userData;
    }
}