<?php
declare(strict_types=1);

namespace App\Application\Session;

/**
 * Session Manager
 * 
 * Clase profesional para el manejo de sesiones de usuario
 * Inspirada en el patrón de Magento 2.4.7
 */
class SessionManager
{
    /**
     * Session keys constants
     */
    private const SESSION_KEY_USER_ID = 'user_id';
    private const SESSION_KEY_USERNAME = 'username';
    private const SESSION_KEY_EMAIL = 'email';
    private const SESSION_KEY_FIRST_NAME = 'first_name';
    private const SESSION_KEY_LAST_NAME = 'last_name';
    private const SESSION_KEY_IS_AUTHENTICATED = 'is_authenticated';
    private const SESSION_KEY_LOGIN_TIME = 'login_time';
    private const SESSION_KEY_LAST_ACTIVITY = 'last_activity';

    /**
     * @var bool Session started flag
     */
    private bool $sessionStarted = false;

    /**
     * @var int Session timeout in seconds (default: 2 hours)
     */
    private int $sessionTimeout = 7200;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Configure session settings
        $this->configureSession();
    }

    /**
     * Configure session settings
     * 
     * @return void
     */
    private function configureSession(): void
    {
        // Set session configuration
        ini_set('session.cookie_httponly', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.cookie_secure', '0'); // Set to 1 in production with HTTPS
        ini_set('session.cookie_samesite', 'Strict');
        
        // Set session name
        session_name('INTEGRATION_SESSION');
    }

    /**
     * Start session
     * 
     * @return bool
     */
    public function start(): bool
    {
        if ($this->sessionStarted) {
            return true;
        }

        if (session_status() === PHP_SESSION_NONE) {
            $result = session_start();
            $this->sessionStarted = $result;
            
            if ($result) {
                $this->updateLastActivity();
                $this->validateSession();
            }
            
            return $result;
        }

        $this->sessionStarted = true;
        return true;
    }

    /**
     * Validate session timeout
     * 
     * @return void
     */
    private function validateSession(): void
    {
        $lastActivity = $this->getLastActivity();
        
        if ($lastActivity && (time() - $lastActivity) > $this->sessionTimeout) {
            $this->destroy();
        }
    }

    /**
     * Update last activity timestamp
     * 
     * @return void
     */
    private function updateLastActivity(): void
    {
        $_SESSION[self::SESSION_KEY_LAST_ACTIVITY] = time();
    }

    /**
     * Check if user is authenticated
     * 
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        $this->start();
        return isset($_SESSION[self::SESSION_KEY_IS_AUTHENTICATED]) && 
               $_SESSION[self::SESSION_KEY_IS_AUTHENTICATED] === true;
    }

    /**
     * Login user
     * 
     * @param array $userData User data
     * @return bool
     */
    public function login(array $userData): bool
    {
        $this->start();
        
        // Regenerate session ID for security
        session_regenerate_id(true);
        
        // Set session data
        $_SESSION[self::SESSION_KEY_USER_ID] = $userData['user_id'] ?? null;
        $_SESSION[self::SESSION_KEY_USERNAME] = $userData['username'] ?? null;
        $_SESSION[self::SESSION_KEY_EMAIL] = $userData['email'] ?? null;
        $_SESSION[self::SESSION_KEY_FIRST_NAME] = $userData['first_name'] ?? null;
        $_SESSION[self::SESSION_KEY_LAST_NAME] = $userData['last_name'] ?? null;
        $_SESSION[self::SESSION_KEY_IS_AUTHENTICATED] = true;
        $_SESSION[self::SESSION_KEY_LOGIN_TIME] = time();
        
        $this->updateLastActivity();
        
        return true;
    }

    /**
     * Logout user
     * 
     * @return bool
     */
    public function logout(): bool
    {
        $this->start();
        
        // Clear all session data
        $_SESSION = [];
        
        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        return session_destroy();
    }

    /**
     * Destroy session
     * 
     * @return bool
     */
    public function destroy(): bool
    {
        return $this->logout();
    }

    /**
     * Get user ID
     * 
     * @return int|null
     */
    public function getUserId(): ?int
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_USER_ID] ?? null;
    }

    /**
     * Get username
     * 
     * @return string|null
     */
    public function getUsername(): ?string
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_USERNAME] ?? null;
    }

    /**
     * Get email
     * 
     * @return string|null
     */
    public function getEmail(): ?string
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_EMAIL] ?? null;
    }

    /**
     * Get first name
     * 
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_FIRST_NAME] ?? null;
    }

    /**
     * Get last name
     * 
     * @return string|null
     */
    public function getLastName(): ?string
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_LAST_NAME] ?? null;
    }

    /**
     * Get login time
     * 
     * @return int|null
     */
    public function getLoginTime(): ?int
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_LOGIN_TIME] ?? null;
    }

    /**
     * Get last activity time
     * 
     * @return int|null
     */
    public function getLastActivity(): ?int
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_LAST_ACTIVITY] ?? null;
    }

    /**
     * Get full name
     * 
     * @return string
     */
    public function getFullName(): string
    {
        $firstName = $this->getFirstName();
        $lastName = $this->getLastName();
        
        if ($firstName && $lastName) {
            return trim($firstName . ' ' . $lastName);
        }
        
        return $firstName ?: $lastName ?: $this->getUsername() ?: 'Usuario';
    }

    /**
     * Set session timeout
     * 
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function setSessionTimeout(int $timeout): void
    {
        $this->sessionTimeout = $timeout;
    }

    /**
     * Get session timeout
     * 
     * @return int
     */
    public function getSessionTimeout(): int
    {
        return $this->sessionTimeout;
    }

    /**
     * Check if session is expired
     * 
     * @return bool
     */
    public function isExpired(): bool
    {
        $lastActivity = $this->getLastActivity();
        
        if (!$lastActivity) {
            return true;
        }
        
        return (time() - $lastActivity) > $this->sessionTimeout;
    }

    /**
     * Get session data as array
     * 
     * @return array
     */
    public function toArray(): array
    {
        $this->start();
        
        return [
            'user_id' => $this->getUserId(),
            'username' => $this->getUsername(),
            'email' => $this->getEmail(),
            'first_name' => $this->getFirstName(),
            'last_name' => $this->getLastName(),
            'full_name' => $this->getFullName(),
            'is_authenticated' => $this->isAuthenticated(),
            'login_time' => $this->getLoginTime(),
            'last_activity' => $this->getLastActivity(),
            'is_expired' => $this->isExpired()
        ];
    }
}
