<?php
declare(strict_types=1);

namespace App\Controllers\Pages;

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\LoggerInterface;
use Slim\Views\Twig;
use App\Application\Session\SessionManager;

/**
 * Index Controller
 * 
 * Controlador principal que maneja la ruta /
 * Muestra login si no está autenticado, dashboard si está autenticado
 */
class IndexController
{
    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var SessionManager
     */
    private SessionManager $sessionManager;

    /**
     * Constructor
     * 
     * @param LoggerInterface $logger
     * @param SessionManager|null $sessionManager
     */
    public function __construct(
        LoggerInterface $logger,
        ?SessionManager $sessionManager = null
    ) {
        $this->logger = $logger;
        $this->sessionManager = $sessionManager ?? new SessionManager();
    }

    /**
     * Handle main route /
     * 
     * @param Request $request
     * @param Response $response
     * @param array $args
     * @return Response
     */
    public function index(Request $request, Response $response, array $args): Response
    {
        $this->sessionManager->start();
        
        // Check if user is authenticated
        if ($this->sessionManager->isAuthenticated()) {
            // User is logged in, show dashboard
            return $this->showDashboard($request, $response, $args);
        } else {
            // User is not logged in, show login form
            return $this->showLogin($request, $response, $args);
        }
    }

    /**
     * Show login form
     * 
     * @param Request $request
     * @param Response $response
     * @param array $args
     * @return Response
     */
    private function showLogin(Request $request, Response $response, array $args): Response
    {
        $this->logger->info('Showing login form for unauthenticated user');
        
        $template = Twig::fromRequest($request);

        $templateArgs = array_merge($args, [
            'title' => 'Login',
            'message' => 'Please login to access the application',
            'show_login' => true
        ]);

        return $template->render($response, 'login.html', $templateArgs);
    }

    /**
     * Show dashboard
     * 
     * @param Request $request
     * @param Response $response
     * @param array $args
     * @return Response
     */
    private function showDashboard(Request $request, Response $response, array $args): Response
    {
        $this->logger->info('Showing dashboard for authenticated user', [
            'user_id' => $this->sessionManager->getUserId(),
            'username' => $this->sessionManager->getUsername()
        ]);
        
        $template = Twig::fromRequest($request);

        $templateArgs = array_merge($args, [
            'title' => 'Dashboard',
            'message' => 'Welcome to the Integration Dashboard',
            'user' => [
                'id' => $this->sessionManager->getUserId(),
                'username' => $this->sessionManager->getUsername(),
                'email' => $this->sessionManager->getEmail(),
                'full_name' => $this->sessionManager->getFullName(),
                'login_time' => $this->sessionManager->getLoginTime(),
                'login_ip' => $this->sessionManager->getLoginIpAddress(),
                'login_attempts' => $this->sessionManager->getLoginAttemptsCount(),
                'remember_me' => $this->sessionManager->isRememberMe()
            ],
            'show_dashboard' => true
        ]);

        return $template->render($response, 'app-skeleton.html', $templateArgs);
    }

    /**
     * Dashboard route (for protected dashboard routes)
     * 
     * @param Request $request
     * @param Response $response
     * @param array $args
     * @return Response
     */
    public function dashboard(Request $request, Response $response, array $args): Response
    {
        // This method is called for /dashboard routes that are protected by SessionMiddleware
        // At this point, we know the user is authenticated because the middleware passed
        
        $sessionManager = $request->getAttribute('session') ?? $this->sessionManager;
        
        $this->logger->info('Accessing protected dashboard route', [
            'user_id' => $sessionManager->getUserId(),
            'username' => $sessionManager->getUsername(),
            'route' => $request->getUri()->getPath()
        ]);
        
        $template = Twig::fromRequest($request);

        $templateArgs = array_merge($args, [
            'title' => 'Dashboard',
            'message' => 'Welcome to the Integration Dashboard',
            'user' => [
                'id' => $sessionManager->getUserId(),
                'username' => $sessionManager->getUsername(),
                'email' => $sessionManager->getEmail(),
                'full_name' => $sessionManager->getFullName(),
                'login_time' => $sessionManager->getLoginTime()
            ],
            'show_dashboard' => true,
            'is_protected_route' => true
        ]);

        return $template->render($response, 'app-skeleton.html', $templateArgs);
    }
}
