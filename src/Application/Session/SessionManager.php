<?php
declare(strict_types=1);

namespace App\Application\Session;

use App\Models\Auth\Login;

/**
 * Session Manager
 *
 * Clase profesional para el manejo de sesiones de usuario
 * Usa el modelo Login como núcleo para todos los datos de usuario
 * Se enfoca únicamente en el manejo de la sesión PHP
 */
class SessionManager
{
    /**
     * Session keys constants
     */
    private const SESSION_KEY_LOGIN_MODEL = 'login_model_data';
    private const SESSION_KEY_IS_AUTHENTICATED = 'is_authenticated';
    private const SESSION_KEY_LAST_ACTIVITY = 'last_activity';

    /**
     * @var bool Session started flag
     */
    private bool $sessionStarted = false;

    /**
     * @var int Session timeout in seconds (default: 2 hours)
     */
    private int $sessionTimeout = 7200;

    /**
     * @var Login Login model instance (core of session data)
     */
    private Login $loginModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Configure session settings
        $this->configureSession();
    }

    /**
     * Configure session settings
     * 
     * @return void
     */
    private function configureSession(): void
    {
        // Set session configuration
        ini_set('session.cookie_httponly', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.cookie_secure', '0'); // Set to 1 in production with HTTPS
        ini_set('session.cookie_samesite', 'Strict');
        
        // Set session name
        session_name('INTEGRATION_SESSION');
    }

    /**
     * Start session
     * 
     * @return bool
     */
    public function start(): bool
    {
        if ($this->sessionStarted) {
            return true;
        }

        if (session_status() === PHP_SESSION_NONE) {
            $result = session_start();
            $this->sessionStarted = $result;
            
            if ($result) {
                $this->updateLastActivity();
                $this->validateSession();
            }
            
            return $result;
        }

        $this->sessionStarted = true;
        return true;
    }

    /**
     * Validate session timeout
     * 
     * @return void
     */
    private function validateSession(): void
    {
        $lastActivity = $this->getLastActivity();
        
        if ($lastActivity && (time() - $lastActivity) > $this->sessionTimeout) {
            $this->destroy();
        }
    }

    /**
     * Update last activity timestamp
     *
     * @return void
     */
    private function updateLastActivity(): void
    {
        $_SESSION[self::SESSION_KEY_LAST_ACTIVITY] = time();
    }

    /**
     * Get login model instance
     *
     * @return Login
     */
    public function getLoginModel(): Login
    {
        if ($this->loginModel === null) {
            $this->start();

            // Try to load from session
            $modelData = $_SESSION[self::SESSION_KEY_LOGIN_MODEL] ?? [];
            $this->loginModel = new Login($modelData);
        }

        return $this->loginModel;
    }

    /**
     * Save login model to session
     *
     * @return void
     */
    private function saveLoginModel(): void
    {
        if ($this->loginModel !== null) {
            $_SESSION[self::SESSION_KEY_LOGIN_MODEL] = $this->loginModel->toArray();
        }
    }

    /**
     * Create login attempt
     *
     * @param array $loginData Login attempt data
     * @return Login
     */
    public function createLoginAttempt(array $loginData): Login
    {
        $this->start();

        // Create new login model with attempt data
        $this->loginModel = new Login([
            Login::FIELD_USERNAME => $loginData['username'] ?? null,
            Login::FIELD_PASSWORD => $loginData['password'] ?? null,
            Login::FIELD_EMAIL => $loginData['email'] ?? null,
            Login::FIELD_IP_ADDRESS => $loginData['ip_address'] ?? null,
            Login::FIELD_USER_AGENT => $loginData['user_agent'] ?? null,
            Login::FIELD_REMEMBER_ME => $loginData['remember_me'] ?? false,
        ]);

        return $this->loginModel;
    }

    /**
     * Validate login attempt
     *
     * @return array Validation errors (empty if valid)
     */
    public function validateLoginAttempt(): array
    {
        $loginModel = $this->getLoginModel();
        return $loginModel->validate();
    }

    /**
     * Check if user is authenticated
     * 
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        $this->start();
        return isset($_SESSION[self::SESSION_KEY_IS_AUTHENTICATED]) && 
               $_SESSION[self::SESSION_KEY_IS_AUTHENTICATED] === true;
    }

    /**
     * Complete successful login
     *
     * @param array $userData User data from authentication
     * @return bool
     */
    public function login(array $userData): bool
    {
        $this->start();

        // Regenerate session ID for security
        session_regenerate_id(true);

        // Get current login model or create new one
        $loginModel = $this->getLoginModel();

        // Mark login as successful
        $loginModel->setIsSuccessful(true);
        $loginModel->setLoginTime(date('Y-m-d H:i:s'));

        // Set user data in session
        $_SESSION[self::SESSION_KEY_USER_DATA] = $userData;
        $_SESSION[self::SESSION_KEY_IS_AUTHENTICATED] = true;
        $_SESSION[self::SESSION_KEY_LOGIN_TIME] = time();

        // Save login model to session
        $this->saveLoginModel();

        $this->updateLastActivity();

        return true;
    }

    /**
     * Complete login with Login model
     *
     * @param Login $loginModel Login model instance
     * @param array $userData User data from authentication
     * @return bool
     */
    public function loginWithModel(Login $loginModel, array $userData): bool
    {
        $this->start();

        // Regenerate session ID for security
        session_regenerate_id(true);

        // Set the login model
        $this->loginModel = $loginModel;

        // Mark login as successful
        $this->loginModel->setIsSuccessful(true);
        $this->loginModel->setLoginTime(date('Y-m-d H:i:s'));

        // Set user data in session
        $_SESSION[self::SESSION_KEY_USER_DATA] = $userData;
        $_SESSION[self::SESSION_KEY_IS_AUTHENTICATED] = true;
        $_SESSION[self::SESSION_KEY_LOGIN_TIME] = time();

        // Save login model to session
        $this->saveLoginModel();

        $this->updateLastActivity();

        return true;
    }

    /**
     * Record failed login attempt
     *
     * @param string $reason Failure reason
     * @return void
     */
    public function recordFailedLogin(string $reason): void
    {
        $loginModel = $this->getLoginModel();
        $loginModel->setIsSuccessful(false);
        $loginModel->setFailureReason($reason);
        $loginModel->incrementAttemptsCount();

        $this->saveLoginModel();
    }

    /**
     * Logout user
     *
     * @return bool
     */
    public function logout(): bool
    {
        $this->start();

        // Clear login model cache
        $this->loginModel = null;

        // Clear all session data
        $_SESSION = [];

        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // Destroy session
        return session_destroy();
    }

    /**
     * Get user data from session
     *
     * @return array
     */
    private function getUserData(): array
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_USER_DATA] ?? [];
    }

    /**
     * Destroy session
     * 
     * @return bool
     */
    public function destroy(): bool
    {
        return $this->logout();
    }

    /**
     * Get user ID
     *
     * @return int|null
     */
    public function getUserId(): ?int
    {
        $userData = $this->getUserData();
        return $userData['user_id'] ?? null;
    }

    /**
     * Get username
     *
     * @return string|null
     */
    public function getUsername(): ?string
    {
        $userData = $this->getUserData();
        $username = $userData['username'] ?? null;

        // Fallback to login model if not in user data
        if (!$username) {
            $loginModel = $this->getLoginModel();
            $username = $loginModel->getUsername();
        }

        return $username;
    }

    /**
     * Get email
     *
     * @return string|null
     */
    public function getEmail(): ?string
    {
        $userData = $this->getUserData();
        $email = $userData['email'] ?? null;

        // Fallback to login model if not in user data
        if (!$email) {
            $loginModel = $this->getLoginModel();
            $email = $loginModel->getEmail();
        }

        return $email;
    }

    /**
     * Get first name
     *
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        $userData = $this->getUserData();
        return $userData['first_name'] ?? null;
    }

    /**
     * Get last name
     *
     * @return string|null
     */
    public function getLastName(): ?string
    {
        $userData = $this->getUserData();
        return $userData['last_name'] ?? null;
    }

    /**
     * Get login time
     * 
     * @return int|null
     */
    public function getLoginTime(): ?int
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_LOGIN_TIME] ?? null;
    }

    /**
     * Get last activity time
     * 
     * @return int|null
     */
    public function getLastActivity(): ?int
    {
        $this->start();
        return $_SESSION[self::SESSION_KEY_LAST_ACTIVITY] ?? null;
    }

    /**
     * Get full name
     * 
     * @return string
     */
    public function getFullName(): string
    {
        $firstName = $this->getFirstName();
        $lastName = $this->getLastName();
        
        if ($firstName && $lastName) {
            return trim($firstName . ' ' . $lastName);
        }
        
        return $firstName ?: $lastName ?: $this->getUsername() ?: 'Usuario';
    }

    /**
     * Set session timeout
     * 
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function setSessionTimeout(int $timeout): void
    {
        $this->sessionTimeout = $timeout;
    }

    /**
     * Get session timeout
     * 
     * @return int
     */
    public function getSessionTimeout(): int
    {
        return $this->sessionTimeout;
    }

    /**
     * Check if session is expired
     * 
     * @return bool
     */
    public function isExpired(): bool
    {
        $lastActivity = $this->getLastActivity();
        
        if (!$lastActivity) {
            return true;
        }
        
        return (time() - $lastActivity) > $this->sessionTimeout;
    }

    /**
     * Get login attempts count
     *
     * @return int
     */
    public function getLoginAttemptsCount(): int
    {
        $loginModel = $this->getLoginModel();
        return $loginModel->getAttemptsCount();
    }

    /**
     * Get last login failure reason
     *
     * @return string|null
     */
    public function getLastFailureReason(): ?string
    {
        $loginModel = $this->getLoginModel();
        return $loginModel->getFailureReason();
    }

    /**
     * Check if current login was successful
     *
     * @return bool
     */
    public function isLoginSuccessful(): bool
    {
        $loginModel = $this->getLoginModel();
        return $loginModel->getIsSuccessful();
    }

    /**
     * Get IP address from login model
     *
     * @return string|null
     */
    public function getLoginIpAddress(): ?string
    {
        $loginModel = $this->getLoginModel();
        return $loginModel->getIpAddress();
    }

    /**
     * Get user agent from login model
     *
     * @return string|null
     */
    public function getLoginUserAgent(): ?string
    {
        $loginModel = $this->getLoginModel();
        return $loginModel->getUserAgent();
    }

    /**
     * Check if remember me was selected
     *
     * @return bool
     */
    public function isRememberMe(): bool
    {
        $loginModel = $this->getLoginModel();
        return $loginModel->getRememberMe();
    }

    /**
     * Get session data as array
     *
     * @return array
     */
    public function toArray(): array
    {
        $this->start();

        return [
            'user_id' => $this->getUserId(),
            'username' => $this->getUsername(),
            'email' => $this->getEmail(),
            'first_name' => $this->getFirstName(),
            'last_name' => $this->getLastName(),
            'full_name' => $this->getFullName(),
            'is_authenticated' => $this->isAuthenticated(),
            'login_time' => $this->getLoginTime(),
            'last_activity' => $this->getLastActivity(),
            'is_expired' => $this->isExpired(),
            'login_attempts' => $this->getLoginAttemptsCount(),
            'last_failure_reason' => $this->getLastFailureReason(),
            'login_ip' => $this->getLoginIpAddress(),
            'remember_me' => $this->isRememberMe()
        ];
    }

    /**
     * Get session data for logging (without sensitive information)
     *
     * @return array
     */
    public function toLogArray(): array
    {
        $loginModel = $this->getLoginModel();
        $sessionData = $this->toArray();

        // Merge with login model log data (which excludes password)
        return array_merge($sessionData, [
            'login_model' => $loginModel->toLogArray()
        ]);
    }
}
