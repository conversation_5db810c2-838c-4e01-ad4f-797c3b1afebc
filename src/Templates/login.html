{% extends 'base/base.twig' %}

{% block styles %}
    <link rel="stylesheet" href="assets/css/login.css" type="text/css" media="all" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" type="text/css" media="all" />
    <style>
        .login-message {
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: 500;
        }
        .login-message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .login-message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="assets/img/logo.png" alt="integration" class="login-logo">
                <h1 class="login-title">Integration API</h1>
            </div>
            <form class="login-form" id="loginForm" action="/auth/login" method="post" autocomplete="off">
                <div id="loginMessage" class="login-message" style="display: none;"></div>
                <div class="form-group">
                    <input type="text" id="username" name="username" class="form-control" placeholder="Email" required >
                    <label for="username"><span><i class="fa fa-user"></i></span></label>
                </div>
                <div class="form-group">
                    <input type="password" id="password" name="password" class="form-control" placeholder="Password" required>
                    <label for="password"><span><i class="fa fa-lock"></i></span></label>
                </div>
                
                <button type="submit" class="btn-primary" id="loginButton">
                    <span id="loginText">Login</span>
                    <span id="loginSpinner" style="display: none;"><i class="fa fa-spinner fa-spin"></i></span>
                </button>
                <div class="login-footer">
                    {% block login_footer %}{% endblock %}
                </div>
            </form>
            <div class="login-copy">
                <p class="text-copy">Copyright &copy; {{ app.copyright_year }} {{ app.company }} All rights reserved.</p>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginButton = document.getElementById('loginButton');
            const loginText = document.getElementById('loginText');
            const loginSpinner = document.getElementById('loginSpinner');
            const loginMessage = document.getElementById('loginMessage');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Disable button and show spinner
                loginButton.disabled = true;
                loginText.style.display = 'none';
                loginSpinner.style.display = 'inline';

                // Hide previous messages
                loginMessage.style.display = 'none';

                // Get form data
                const formData = new FormData(loginForm);

                // Send AJAX request
                fetch('/auth/login', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        showMessage('Login successful! Redirecting...', 'success');

                        // Redirect after a short delay
                        setTimeout(() => {
                            window.location.href = data.redirect || '/';
                        }, 1000);
                    } else {
                        // Show error message
                        showMessage(data.message || 'Login failed. Please try again.', 'error');

                        // Re-enable button
                        resetButton();
                    }
                })
                .catch(error => {
                    console.error('Login error:', error);
                    showMessage('An error occurred. Please try again.', 'error');
                    resetButton();
                });
            });

            function showMessage(message, type) {
                loginMessage.textContent = message;
                loginMessage.className = 'login-message ' + type;
                loginMessage.style.display = 'block';
            }

            function resetButton() {
                loginButton.disabled = false;
                loginText.style.display = 'inline';
                loginSpinner.style.display = 'none';
            }
        });
    </script>
{% endblock %}
