{% extends 'base/base.twig' %}

{% block styles %}
    <link rel="stylesheet" href="assets/css/dashboard.css" type="text/css" media="all" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" type="text/css" media="all" />
    <style>
        .dashboard-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Roboto', sans-serif;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .dashboard-header {
            margin-bottom: 30px;
        }
        
        .dashboard-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }
        
        .dashboard-title {
            color: #333;
            font-size: 2.5rem;
            font-weight: 300;
            margin: 0 0 10px 0;
        }
        
        .dashboard-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin: 0;
        }
        
        .welcome-message {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            border-left: 4px solid #667eea;
        }
        
        .welcome-text {
            font-size: 1.3rem;
            color: #333;
            margin: 0 0 15px 0;
        }
        
        .user-info {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        
        .user-info h3 {
            color: #495057;
            margin: 0 0 15px 0;
            font-size: 1.1rem;
        }
        
        .user-detail {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .user-detail:last-child {
            border-bottom: none;
        }
        
        .user-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .user-value {
            color: #495057;
        }
        
        .dashboard-actions {
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .footer-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="dashboard-container">
        <div class="dashboard-card">
            <div class="dashboard-header">
                <img src="assets/img/logo.png" alt="Integration" class="dashboard-logo">
                <h1 class="dashboard-title">Integration Dashboard</h1>
                <p class="dashboard-subtitle">KnownOnline Integration API</p>
            </div>
            
            <div class="welcome-message">
                <p class="welcome-text">
                    <i class="fa fa-check-circle" style="color: #28a745; margin-right: 10px;"></i>
                    Ingrese solo eso
                </p>
                
                {% if user %}
                <div class="user-info">
                    <h3><i class="fa fa-user"></i> Información del Usuario</h3>
                    
                    <div class="user-detail">
                        <span class="user-label">Estado:</span>
                        <span class="user-value">
                            <span class="status-indicator"></span>
                            Conectado
                        </span>
                    </div>
                    
                    {% if user.username %}
                    <div class="user-detail">
                        <span class="user-label">Usuario:</span>
                        <span class="user-value">{{ user.username }}</span>
                    </div>
                    {% endif %}
                    
                    {% if user.full_name %}
                    <div class="user-detail">
                        <span class="user-label">Nombre:</span>
                        <span class="user-value">{{ user.full_name }}</span>
                    </div>
                    {% endif %}
                    
                    {% if user.email %}
                    <div class="user-detail">
                        <span class="user-label">Email:</span>
                        <span class="user-value">{{ user.email }}</span>
                    </div>
                    {% endif %}
                    
                    {% if user.login_time %}
                    <div class="user-detail">
                        <span class="user-label">Sesión iniciada:</span>
                        <span class="user-value">{{ user.login_time|date('d/m/Y H:i:s') }}</span>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            
            <div class="dashboard-actions">
                <a href="/dashboard" class="btn btn-primary">
                    <i class="fa fa-tachometer"></i> Dashboard
                </a>
                <a href="/auth/logout" class="btn btn-secondary">
                    <i class="fa fa-sign-out"></i> Cerrar Sesión
                </a>
            </div>
            
            <div class="footer-info">
                <p>
                    <i class="fa fa-shield"></i> 
                    Sesión segura activa
                    {% if is_protected_route %}
                    | Ruta protegida
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Auto-refresh session status every 5 minutes
        setInterval(function() {
            fetch('/auth/status')
                .then(response => response.json())
                .then(data => {
                    if (!data.authenticated) {
                        window.location.href = '/';
                    }
                })
                .catch(error => {
                    console.log('Session check failed:', error);
                });
        }, 300000); // 5 minutes
    </script>
{% endblock %}
