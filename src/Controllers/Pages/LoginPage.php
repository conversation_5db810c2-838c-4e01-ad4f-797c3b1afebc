<?php
declare(strict_types=1);

namespace App\Controllers\Pages;

use App\Helper\ControllersExceptions;
use App\Models\Pages\Users\UserFactory;
use App\Models\Auth\Login;
use App\Application\Session\SessionManager;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\LoggerInterface;
use Slim\Views\Twig;
use Slim\Psr7\Response as SlimResponse;

class LoginPage
{
    protected ControllersExceptions $controller;
    protected UserFactory $userFactory;
    private LoggerInterface $logger;
    private SessionManager $sessionManager;

    public function __construct(
        LoggerInterface $logger,
        ControllersExceptions $controller,
        UserFactory $userFactory,
        ?SessionManager $sessionManager = null
    ) {
        $this->userFactory = $userFactory;
        $this->controller = $controller;
        $this->logger = $logger;
        $this->sessionManager = $sessionManager ?? new SessionManager();
    }

    public function IndexPage(Request $request, Response $response, array $args)
    {
        $template = Twig::FromRequest($request);

        $args = array_merge($args, [
            'title' => 'Login',
            'message' => 'Please login'
        ]);
        return $template->render($response, 'login.html', $args);
    }

    public function AuthenticateUser(Request $request, Response $response, array $args)
    {
        $controller = [];
        $paramsExpected = ['username', 'password'];
        $message = 'Invalid request: missing parameters';

        try {
            // Validate request parameters
            $controller = $this->controller->HelperControllersExceptions($request->getParsedBody(), $paramsExpected);
            if (isset($controller['exception'])) {
                throw new \RuntimeException($message);
            }

            // Create login model
            $loginModel = new Login([
                'username' => $controller['username'],
                'password' => $controller['password'],
                'ip_address' => $request->getServerParams()['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $request->getHeaderLine('User-Agent')
            ]);

            // Validate login data
            $validationErrors = $loginModel->validate();
            if (!empty($validationErrors)) {
                throw new \RuntimeException('Validation failed: ' . implode(', ', $validationErrors));
            }

            // Authenticate user
            $userData = $this->userFactory->getUserEmailOrUsername($controller['username']);
            if (isset($userData['exception'])) {
                $loginModel->setIsSuccessful(false);
                $loginModel->setFailureReason('User not found');
                throw new \RuntimeException('Authentication failed');
            }

            // TODO: Verify password hash here
            // For now, we'll assume authentication is successful if user exists

            // Login successful - create session
            $this->sessionManager->login([
                'user_id' => $userData['user_id'] ?? 1,
                'username' => $userData['username'] ?? $controller['username'],
                'email' => $userData['email'] ?? null,
                'first_name' => $userData['first_name'] ?? null,
                'last_name' => $userData['last_name'] ?? null
            ]);

            $loginModel->setIsSuccessful(true);

            $this->logger->info('User authenticated successfully', [
                'username' => $controller['username'],
                'user_id' => $userData['user_id'] ?? 1
            ]);

            // Return success response with redirect
            $responseData = [
                'success' => true,
                'message' => 'Login successful',
                'redirect' => '/'
            ];

            $response->getBody()->write(json_encode($responseData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));
            return $response
                ->withHeader('Content-Type', 'application/json')
                ->withStatus(200);

        } catch (\Throwable $th) {
            $this->logger->error("Authentication error: " . $th->getMessage(), [
                'username' => $controller['username'] ?? 'unknown',
                'error' => $th->getMessage()
            ]);

            $errorResponse = [
                'success' => false,
                'error' => true,
                'message' => 'Authentication failed. Please check your credentials and try again.'
            ];

            $response->getBody()->write(json_encode($errorResponse, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));
            return $response
                ->withHeader('Content-Type', 'application/json')
                ->withStatus(401);
        }
    }

    /**
     * Logout user
     *
     * @param Request $request
     * @param Response $response
     * @param array $args
     * @return Response
     */
    public function logout(Request $request, Response $response, array $args): Response
    {
        try {
            $this->sessionManager->start();
            $username = $this->sessionManager->getUsername();

            // Destroy session
            $this->sessionManager->logout();

            $this->logger->info('User logged out successfully', [
                'username' => $username
            ]);

            // Redirect to home page
            return $response
                ->withHeader('Location', '/')
                ->withStatus(302);

        } catch (\Throwable $th) {
            $this->logger->error("Logout error: " . $th->getMessage());

            // Even if there's an error, redirect to home
            return $response
                ->withHeader('Location', '/')
                ->withStatus(302);
        }
    }
}
