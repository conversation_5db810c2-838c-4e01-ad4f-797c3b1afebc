<?php
declare(strict_types=1);

use App\Application\Helper\PathManager;
use App\Controllers\Pages\LoginPage;
use App\Controllers\Pages\IndexController;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use App\Middleware\JwtAuthMiddleware;
use App\Middleware\SessionMiddleware;
use Slim\App;
use Slim\Interfaces\RouteCollectorProxyInterface as Group;

/**
 * Rutas para páginas web
 *
 * Este archivo define todas las rutas relacionadas con las páginas web de la aplicación
 */
return function (App $app, $ambient) {

    // Ruta principal - maneja login y dashboard
    $app->get('/', IndexController::class . ':index')->setName('home')->setArguments($ambient);

    // Grupo de autenticación
    $app->group('/auth', function (Group $group) {
        $group->post('/login', LoginPage::class . ':AuthenticateUser');
        $group->get('/logout', LoginPage::class . ':logout');
    });

    // Rutas protegidas que requieren sesión activa
    $app->group('/dashboard', function (Group $group) use ($ambient) {
        $group->get('', IndexController::class . ':dashboard')->setArguments($ambient);
        // Aquí se pueden agregar más rutas del dashboard
    })->add(new SessionMiddleware());
};
